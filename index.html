<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Document</title>
    <link rel="stylesheet" href="css/bootstrap.css" />
    <style>
      /* Custom styles for nav buttons */
      .nav-btn {
        background: none;
        border: none;
        color: rgba(0, 0, 0, 0.55);
        padding: 0.5rem 1rem;
        margin: 0 0.25rem;
        border-radius: 0.375rem;
        font-size: 1rem;
        font-weight: 400;
        text-decoration: none;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
      }

      .nav-btn:hover {
        color: rgba(0, 0, 0, 0.7);
        background-color: rgba(0, 0, 0, 0.05);
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
      }

      .nav-btn.active {
        color: rgba(0, 0, 0, 0.9);
        background-color: rgba(0, 0, 0, 0.1);
      }

      .nav-btn:focus {
        outline: none;
        box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
      }

      /* Animation for button press */
      .nav-btn:active {
        transform: translateY(0);
        transition: transform 0.1s ease;
      }

      /* Ripple effect animation */
      .nav-btn::before {
        content: '';
        position: absolute;
        top: 50%;
        left: 50%;
        width: 0;
        height: 0;
        border-radius: 50%;
        background: rgba(0, 0, 0, 0.1);
        transform: translate(-50%, -50%);
        transition: width 0.3s ease, height 0.3s ease;
      }

      .nav-btn:hover::before {
        width: 100%;
        height: 100%;
      }

      /* Ensure dropdown items are also styled */
      .dropdown-item {
        transition: all 0.2s ease;
      }

      .dropdown-item:hover {
        transform: translateX(5px);
        background-color: #f8f9fa;
      }
    </style>
  </head>
  <body>
    <nav class="navbar navbar-expand-lg bg-body-tertiary">
      <div class="container-fluid">
        <!-- Left side: Logo -->
        <div class="navbar-brand-container" style="flex: 1;">
          <a class="navbar-brand" href="#"
            ><img
              src="img/Logo_Desa_Wisata_Klabili-removebg-preview.png"
              alt=""
              width="100px"
          /></a>
        </div>

        <button
          class="navbar-toggler"
          type="button"
          data-bs-toggle="collapse"
          data-bs-target="#navbarSupportedContent"
          aria-controls="navbarSupportedContent"
          aria-expanded="false"
          aria-label="Toggle navigation"
        >
          <span class="navbar-toggler-icon"></span>
        </button>

        <div class="collapse navbar-collapse" id="navbarSupportedContent">
          <!-- Center: Navigation items -->
          <ul class="navbar-nav mx-auto mb-2 mb-lg-0 justify-content-center" style="flex: 1;">
            <li class="nav-item">
              <button class="btn nav-btn active" type="button" onclick="navigateTo('index.html')">
                Home
              </button>
            </li>

            <li class="nav-item dropdown">
              <button
                class="btn nav-btn dropdown-toggle"
                type="button"
                data-bs-toggle="dropdown"
                aria-expanded="false"
              >
                About Us
              </button>
              <ul class="dropdown-menu">
                <li><button class="dropdown-item" type="button" onclick="navigateTo('#')">Action</button></li>
                <li><button class="dropdown-item" type="button" onclick="navigateTo('#')">Another action</button></li>
                <li><hr class="dropdown-divider" /></li>
                <li>
                  <button class="dropdown-item" type="button" onclick="navigateTo('#')">Something else here</button>
                </li>
              </ul>
            </li>

            <li class="nav-item">
              <button class="btn nav-btn" type="button" onclick="navigateTo('index.html')">
                Facilities
              </button>
            </li>

            <li class="nav-item">
              <button class="btn nav-btn" type="button" onclick="navigateTo('index.html')">
                Culture
              </button>
            </li>

            <li class="nav-item">
              <button class="btn nav-btn" type="button" onclick="navigateTo('index.html')">
                Village Information
              </button>
            </li>

            <li class="nav-item">
              <button class="btn nav-btn" type="button" onclick="navigateTo('index.html')">
                Gallery
              </button>
            </li>
          </ul>

          <!-- Right side: Language Toggle Switch -->
          <div class="navbar-nav" style="flex: 1; justify-content: flex-end;">
            <div class="nav-item d-flex align-items-center">
              <span class="me-2 text-muted small fw-bold">EN</span>
              <div class="form-check form-switch">
                <input
                  class="form-check-input"
                  type="checkbox"
                  role="switch"
                  id="languageSwitch"
                  onchange="toggleLanguage()"
                />
              </div>
              <span class="ms-2 text-muted small fw-bold">ID</span>
            </div>
          </div>
        </div>
      </div>
    </nav>
  </body>
  <script src="js/bootstrap.bundle.js"></script>
  <script>
    // Navigation function for buttons
    function navigateTo(url) {
      if (url && url !== '#') {
        window.location.href = url;
      }
    }

    // Set active button based on current page
    function setActiveNavButton() {
      const currentPage = window.location.pathname.split('/').pop() || 'index.html';
      const navButtons = document.querySelectorAll('.nav-btn');

      navButtons.forEach(button => {
        button.classList.remove('active');
        const buttonText = button.textContent.trim().toLowerCase();

        if (currentPage === 'index.html' && buttonText === 'home') {
          button.classList.add('active');
        }
        // Add more conditions here for other pages when you create them
      });
    }

    // Add ripple effect to buttons
    function addRippleEffect() {
      const buttons = document.querySelectorAll('.nav-btn');

      buttons.forEach(button => {
        button.addEventListener('click', function(e) {
          // Create ripple element
          const ripple = document.createElement('span');
          const rect = this.getBoundingClientRect();
          const size = Math.max(rect.width, rect.height);
          const x = e.clientX - rect.left - size / 2;
          const y = e.clientY - rect.top - size / 2;

          ripple.style.width = ripple.style.height = size + 'px';
          ripple.style.left = x + 'px';
          ripple.style.top = y + 'px';
          ripple.classList.add('ripple');

          // Add ripple styles
          ripple.style.position = 'absolute';
          ripple.style.borderRadius = '50%';
          ripple.style.background = 'rgba(255, 255, 255, 0.6)';
          ripple.style.transform = 'scale(0)';
          ripple.style.animation = 'ripple 0.6s linear';
          ripple.style.pointerEvents = 'none';

          this.appendChild(ripple);

          // Remove ripple after animation
          setTimeout(() => {
            ripple.remove();
          }, 600);
        });
      });
    }

    // Language toggle function
    function toggleLanguage() {
      const languageSwitch = document.getElementById("languageSwitch");
      const isIndonesian = languageSwitch.checked;

      if (isIndonesian) {
        // Switch to Indonesian
        console.log("Language changed to Indonesian");
        // Add your Indonesian language implementation here
        // Example: document.querySelector('html').setAttribute('lang', 'id');
      } else {
        // Switch to English
        console.log("Language changed to English");
        // Add your English language implementation here
        // Example: document.querySelector('html').setAttribute('lang', 'en');
      }

      // You can add more language switching logic here
      // For example, changing text content, loading different content, etc.
    }

    // Initialize when page loads
    document.addEventListener('DOMContentLoaded', function() {
      setActiveNavButton();
      addRippleEffect();
    });
  </script>

  <style>
    /* Keyframe for ripple animation */
    @keyframes ripple {
      to {
        transform: scale(4);
        opacity: 0;
      }
    }
  </style>
</html>
