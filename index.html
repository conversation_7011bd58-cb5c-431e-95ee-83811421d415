<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Document</title>
    <link rel="stylesheet" href="css/bootstrap.css" />
  </head>
  <body>
    <nav class="navbar navbar-expand-lg bg-body-tertiary">
      <div class="container-fluid">
        <!-- Left side: Logo -->
        <div class="navbar-brand-container" style="flex: 1;">
          <a class="navbar-brand" href="#"
            ><img
              src="img/Logo_Desa_Wisata_Klabili-removebg-preview.png"
              alt=""
              width="100px"
          /></a>
        </div>

        <button
          class="navbar-toggler"
          type="button"
          data-bs-toggle="collapse"
          data-bs-target="#navbarSupportedContent"
          aria-controls="navbarSupportedContent"
          aria-expanded="false"
          aria-label="Toggle navigation"
        >
          <span class="navbar-toggler-icon"></span>
        </button>

        <div class="collapse navbar-collapse" id="navbarSupportedContent">
          <!-- Center: Navigation items -->
          <ul class="navbar-nav mx-auto mb-2 mb-lg-0 justify-content-center" style="flex: 1;">
            <li class="nav-item">
              <a class="nav-link active" aria-current="page" href="index.html">Home</a>
            </li>

            <li class="nav-item dropdown">
              <a
                class="nav-link dropdown-toggle"
                href="#"
                role="button"
                data-bs-toggle="dropdown"
                aria-expanded="false"
              >
                About Us
              </a>
              <ul class="dropdown-menu">
                <li><a class="dropdown-item" href="#">Action</a></li>
                <li><a class="dropdown-item" href="#">Another action</a></li>
                <li><hr class="dropdown-divider" /></li>
                <li>
                  <a class="dropdown-item" href="#">Something else here</a>
                </li>
              </ul>
            </li>

            <li class="nav-item">
              <a class="nav-link active" aria-current="page" href="index.html">Facilities</a>
            </li>

            <li class="nav-item">
              <a class="nav-link active" aria-current="page" href="index.html">Culture</a>
            </li>

            <li class="nav-item">
              <a class="nav-link active" aria-current="page" href="index.html">Village Information</a>
            </li>

            <li class="nav-item">
              <a class="nav-link active" aria-current="page" href="index.html">Gallery</a>
            </li>
          </ul>

          <!-- Right side: Language Toggle Switch -->
          <div class="navbar-nav" style="flex: 1; justify-content: flex-end;">
            <div class="nav-item d-flex align-items-center">
              <span class="me-2 text-muted small fw-bold">EN</span>
              <div class="form-check form-switch">
                <input
                  class="form-check-input"
                  type="checkbox"
                  role="switch"
                  id="languageSwitch"
                  onchange="toggleLanguage()"
                />
              </div>
              <span class="ms-2 text-muted small fw-bold">ID</span>
            </div>
          </div>
        </div>
      </div>
    </nav>
  </body>
  <script src="js/bootstrap.bundle.js"></script>
  <script>
    function toggleLanguage() {
      const languageSwitch = document.getElementById("languageSwitch");
      const isIndonesian = languageSwitch.checked;

      if (isIndonesian) {
        // Switch to Indonesian
        console.log("Language changed to Indonesian");
        // Add your Indonesian language implementation here
        // Example: document.querySelector('html').setAttribute('lang', 'id');
      } else {
        // Switch to English
        console.log("Language changed to English");
        // Add your English language implementation here
        // Example: document.querySelector('html').setAttribute('lang', 'en');
      }

      // You can add more language switching logic here
      // For example, changing text content, loading different content, etc.
    }
  </script>
</html>
