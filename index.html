<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Document</title>
    <!-- Bootstrap CSS -->
    <link rel="stylesheet" href="css/bootstrap/bootstrap.css" />
    <!-- Custom CSS -->
    <link rel="stylesheet" href="css/custom.css" />
  </head>
  <body>
    <nav class="navbar navbar-expand-lg bg-body-tertiary">
      <div class="container-fluid">
        <!-- Left side: Logo -->
        <div class="navbar-brand-container" style="flex: 1;">
          <a class="navbar-brand" href="#"
            ><img
              src="img/Logo_Desa_Wisata_Klabili-removebg-preview.png"
              alt=""
              width="100px"
          /></a>
        </div>

        <button
          class="navbar-toggler"
          type="button"
          data-bs-toggle="collapse"
          data-bs-target="#navbarSupportedContent"
          aria-controls="navbarSupportedContent"
          aria-expanded="false"
          aria-label="Toggle navigation"
        >
          <span class="navbar-toggler-icon"></span>
        </button>

        <div class="collapse navbar-collapse" id="navbarSupportedContent">
          <!-- Center: Navigation items -->
          <ul class="navbar-nav mx-auto mb-2 mb-lg-0 justify-content-center" style="flex: 1;">
            <li class="nav-item">
              <button class="btn nav-btn active" type="button" onclick="navigateTo('index.html')">
                Home
              </button>
            </li>

            <li class="nav-item dropdown">
              <button
                class="btn nav-btn dropdown-toggle"
                type="button"
                data-bs-toggle="dropdown"
                aria-expanded="false"
              >
                About Us
              </button>
              <ul class="dropdown-menu">
                <li><button class="dropdown-item" type="button" onclick="navigateTo('#')">Action</button></li>
                <li><button class="dropdown-item" type="button" onclick="navigateTo('#')">Another action</button></li>
                <li><hr class="dropdown-divider" /></li>
                <li>
                  <button class="dropdown-item" type="button" onclick="navigateTo('#')">Something else here</button>
                </li>
              </ul>
            </li>

            <li class="nav-item">
              <button class="btn nav-btn" type="button" onclick="navigateTo('index.html')">
                Facilities
              </button>
            </li>

            <li class="nav-item">
              <button class="btn nav-btn" type="button" onclick="navigateTo('index.html')">
                Culture
              </button>
            </li>

            <li class="nav-item">
              <button class="btn nav-btn" type="button" onclick="navigateTo('index.html')">
                Village Information
              </button>
            </li>

            <li class="nav-item">
              <button class="btn nav-btn" type="button" onclick="navigateTo('index.html')">
                Gallery
              </button>
            </li>
          </ul>

          <!-- Right side: Language Toggle Switch -->
          <div class="navbar-nav" style="flex: 1; justify-content: flex-end;">
            <div class="nav-item d-flex align-items-center">
              <span class="me-2 text-muted small fw-bold">EN</span>
              <div class="form-check form-switch">
                <input
                  class="form-check-input"
                  type="checkbox"
                  role="switch"
                  id="languageSwitch"
                  onchange="toggleLanguage()"
                />
              </div>
              <span class="ms-2 text-muted small fw-bold">ID</span>
            </div>
          </div>
        </div>
      </div>
    </nav>
  </body>
  <!-- Bootstrap JS -->
  <script src="js/bootstrap/bootstrap.bundle.js"></script>
  <!-- Custom JS -->
  <script src="js/custom.js"></script>
</html>
