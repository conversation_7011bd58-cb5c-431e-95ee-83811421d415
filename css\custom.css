/* Custom styles for nav buttons */
.nav-btn {
  background: none;
  border: none;
  color: rgba(0, 0, 0, 0.55);
  padding: 0.5rem 1rem;
  margin: 0 0.25rem;
  border-radius: 0.375rem;
  font-size: 1rem;
  font-weight: 400;
  text-decoration: none;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.nav-btn:hover {
  color: rgba(0, 0, 0, 0.7);
  background-color: rgba(0, 0, 0, 0.05);
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.nav-btn.active {
  color: rgba(0, 0, 0, 0.9);
  background-color: rgba(0, 0, 0, 0.1);
}

.nav-btn:focus {
  outline: none;
  box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

/* Animation for button press */
.nav-btn:active {
  transform: translateY(0);
  transition: transform 0.1s ease;
}

/* Ripple effect animation */
.nav-btn::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  border-radius: 50%;
  background: rgba(0, 0, 0, 0.1);
  transform: translate(-50%, -50%);
  transition: width 0.3s ease, height 0.3s ease;
}

.nav-btn:hover::before {
  width: 100%;
  height: 100%;
}

/* Ensure dropdown items are also styled */
.dropdown-item {
  transition: all 0.2s ease;
}

.dropdown-item:hover {
  transform: translateX(5px);
  background-color: #f8f9fa;
}

/* Keyframe for ripple animation */
@keyframes ripple {
  to {
    transform: scale(4);
    opacity: 0;
  }
}
